<?php
// Prepare data for the section layout similar to regular landing pages
$activitiesContent = false;
$itinerariesContent = false;

// Check if the landing page has activities or itineraries
if (!empty($landingPage['Activity'])) {
    $activitiesContent = $this->element('content_blocks--activities', array(
        'activities' => $landingPage['Activity'],
    ));
}

if (!empty($landingPage['Itinerary'])) {
    $itinerariesContent = $this->element('content_blocks--itineraries', array(
        'itineraries' => $landingPage['Itinerary'],
    ));
}

// Generate contextual navigation for sidebar based on current page position
$farWideNavigation = array();

// Get all Far & Wide menu items from database
$navigationMenuModel = ClassRegistry::init('NavigationMenu');
$farWideItems = $navigationMenuModel->find('threaded', array(
    'conditions' => array(
        'NavigationMenu.menu_type' => 'far_wide',
        'NavigationMenu.published' => 1
    ),
    'fields' => array(
        'NavigationMenu.id',
        'NavigationMenu.parent_id',
        'NavigationMenu.name',
        'NavigationMenu.url',
        'NavigationMenu.page_id'
    ),
    'order' => 'NavigationMenu.lft ASC',
    'recursive' => -1
));

// Build contextual navigation structure for sidebar
if (!empty($farWideItems)) {
    $currentUrl = $navigationMenu['NavigationMenu']['url'];
    $currentMenuId = $navigationMenu['NavigationMenu']['id'];

    // Find the current menu item and its context
    $currentItem = null;
    $parentItem = null;
    $siblings = array();

    // Search through the top-level items (South America, Worldwide)
    foreach ($farWideItems as $topLevelItem) {
        if ($topLevelItem['NavigationMenu']['id'] == $currentMenuId) {
            // Current item is a top-level section (e.g., South America, Worldwide)
            $currentItem = $topLevelItem;
            $siblings = $farWideItems; // All top-level sections
            break;
        }

        // Check if current item is a child (e.g., Cruises under Worldwide)
        if (!empty($topLevelItem['children'])) {
            foreach ($topLevelItem['children'] as $child) {
                if ($child['NavigationMenu']['id'] == $currentMenuId) {
                    // Current item is under a parent section (e.g., Cruises under Worldwide)
                    $currentItem = $child;
                    $parentItem = $topLevelItem;
                    $siblings = $topLevelItem['children']; // Siblings under the same parent
                    break 2;
                }
            }
        }
    }

    // Build navigation based on context
    if ($currentItem && $parentItem) {
        // Current page is a child item (e.g., Cruises) - show only siblings (no parent container)
        $farWideNavigation = array();

        // Add siblings (including current item) directly, skip non-navigable parent
        foreach ($siblings as $sibling) {
            $siblingUrl = $sibling['NavigationMenu']['url'];
            $isSelected = ($sibling['NavigationMenu']['id'] == $currentMenuId);

            // Only add navigable items (skip items with # or empty URLs)
            if (!empty($siblingUrl) && $siblingUrl !== '#') {
                $farWideNavigation[] = array(
                    'text' => $sibling['NavigationMenu']['name'],
                    'url' => $siblingUrl,
                    'selected' => $isSelected
                );
            }
        }
    } elseif ($currentItem) {
        // Current page is a top-level section (e.g., South America) - show only navigable top-level sections
        $farWideNavigation = array();

        foreach ($siblings as $sibling) {
            $siblingUrl = $sibling['NavigationMenu']['url'];
            $isSelected = ($sibling['NavigationMenu']['id'] == $currentMenuId);

            // Only add navigable items (skip items with # or empty URLs)
            if (!empty($siblingUrl) && $siblingUrl !== '#') {
                $siblingItem = array(
                    'text' => $sibling['NavigationMenu']['name'],
                    'url' => $siblingUrl,
                    'selected' => $isSelected,
                    'has_children' => false,
                    'children' => array()
                );

                // Add navigable children if this sibling is selected
                if ($isSelected && !empty($sibling['children'])) {
                    foreach ($sibling['children'] as $child) {
                        $childUrl = $child['NavigationMenu']['url'];

                        // Only add navigable children
                        if (!empty($childUrl) && $childUrl !== '#') {
                            $siblingItem['children'][] = array(
                                'text' => $child['NavigationMenu']['name'],
                                'url' => $childUrl,
                                'selected' => false
                            );
                            $siblingItem['has_children'] = true;
                        }
                    }
                }

                $farWideNavigation[] = $siblingItem;
            }
        }
    }
}

// Create sidebar content with Far & Wide navigation
$sideBarContent = $this->element('sidebar', array(
    'modifier' => 'far-wide',
    'navigation' => $farWideNavigation
));

// Use the section header with the navigation menu name as the header
echo $this->element('section_header', array(
    'sectionData' => array(
        'LandingPage' => array(
            'name' => $navigationMenu['NavigationMenu']['name']
        ),
        'MainImage' => !empty($landingPage['BannerImage']) ? $landingPage['BannerImage'] : (!empty($landingPage['MainImage']) ? $landingPage['MainImage'] : null)
    ),
    'sectionModel' => 'LandingPage',
    'activitiesContent' => $activitiesContent,
    'itinerariesContent' => $itinerariesContent,
));

$sectionContent = $this->element('content_blocks', array(
    'modifier'      => 'landing-pages',
    'contentBlocks' => $contentBlocks
));

echo $this->element('modules/section_content', array(
    'activitiesContent' => $activitiesContent,
    'itinerariesContent' => $itinerariesContent,
    'sectionContent' => $sectionContent,
    'sideBarContent' => $sideBarContent,
));

echo $this->element('section_footer', array(
  'hideRightSidebar' => true
));

// Set up meta tags from the landing page
if (!empty($landingPage['meta_description'])) {
    $this->set('metaDescription', $landingPage['meta_description']);
}

if (!empty($landingPage['meta_keywords'])) {
    $this->set('metaKeywords', $landingPage['meta_keywords']);
}

if (!empty($landingPage['google_tracking'])) {
    echo $landingPage['google_tracking'];
}
?>
