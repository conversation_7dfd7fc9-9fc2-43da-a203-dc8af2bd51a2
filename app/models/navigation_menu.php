<?php

class NavigationMenu extends AppModel {

    var $name = 'NavigationMenu';
    var $actsAs = array('TreeCounterCache', 'Publishable');
    var $displayField = 'name';
    var $order = 'NavigationMenu.lft ASC';

    var $validate = array(
        'name' => array(
            'notEmpty' => array(
                'rule' => 'notEmpty',
                'required' => true,
                'allowEmpty' => false,
                'message' => 'Name is required',
            ),
        ),
        'url' => array(
            'notEmpty' => array(
                'rule' => 'notEmpty',
                'required' => true,
                'allowEmpty' => false,
                'message' => 'URL is required',
            ),
        ),
        'menu_type' => array(
            'notEmpty' => array(
                'rule' => 'notEmpty',
                'required' => true,
                'allowEmpty' => false,
                'message' => 'Menu type is required',
            ),
            'inList' => array(
                'rule' => array('inList', array('far_wide', 'main_nav', 'footer')),
                'message' => 'Please select a valid menu type',
            ),
        ),
        'parent_id' => array(
            'numeric' => array(
                'rule' => 'numeric',
                'required' => false,
                'allowEmpty' => true,
                'message' => 'Parent ID must be numeric',
            ),
        ),
        'order' => array(
            'numeric' => array(
                'rule' => 'numeric',
                'required' => false,
                'allowEmpty' => true,
                'message' => 'Order must be numeric',
            ),
        ),
    );

    var $belongsTo = array(
        'Parent' => array(
            'className' => 'NavigationMenu',
            'foreignKey' => 'parent_id'
        ),
        'Page' => array(
            'className' => 'Page',
            'foreignKey' => 'page_id'
        )
    );

    /**
     * Get the associated page or landing page for this navigation menu item
     *
     * @param int $id Navigation menu ID
     * @return array|null Page or landing page data
     */
    function getAssociatedPage($id) {
        $navigationMenu = $this->findById($id);

        if (empty($navigationMenu['NavigationMenu']['page_id'])) {
            return null;
        }

        $pageId = $navigationMenu['NavigationMenu']['page_id'];

        // Check if this is a landing page reference (starts with LP-)
        if (strpos($pageId, 'LP-') === 0) {
            $landingPageId = str_replace('LP-', '', $pageId);
            $landingPage = ClassRegistry::init('LandingPage')->findById($landingPageId);

            if (!empty($landingPage)) {
                // Return in a format similar to Page for consistency
                return array(
                    'Page' => array(
                        'id' => $landingPage['LandingPage']['id'],
                        'meta_title' => $landingPage['LandingPage']['meta_title'],
                        'slug' => $landingPage['LandingPage']['slug'],
                        'type' => 'landing_page'
                    ),
                    'LandingPage' => $landingPage['LandingPage']
                );
            }
        } else {
            // Regular page
            $page = ClassRegistry::init('Page')->findById($pageId);

            if (!empty($page)) {
                $page['Page']['type'] = 'page';
                return $page;
            }
        }

        return null;
    }

    /**
     * Get menu items for a specific menu type
     *
     * @param string $menuType The menu type to retrieve
     * @param bool $publishedOnly Whether to only return published items
     * @param bool $preserveHashUrls Whether to preserve # URLs for mmenu compatibility
     * @return array Hierarchical array of menu items
     */
    function getMenuItems($menuType, $publishedOnly = true, $preserveHashUrls = false) {
        $conditions = array('NavigationMenu.menu_type' => $menuType);

        if ($publishedOnly) {
            $conditions['NavigationMenu.published'] = 1;
        }

        $items = $this->find('threaded', array(
            'conditions' => $conditions,
            'fields' => array(
                'NavigationMenu.id',
                'NavigationMenu.parent_id',
                'NavigationMenu.name',
                'NavigationMenu.url',
                'NavigationMenu.menu_type',
                'NavigationMenu.order',
                'NavigationMenu.published',
                'NavigationMenu.lft',
                'NavigationMenu.rght'
            ),
            'order' => 'NavigationMenu.lft ASC',
            'recursive' => -1
        ));

        return $this->_processMenuItems($items, $preserveHashUrls);
    }

    /**
     * Process menu items into the format expected by navigation templates
     *
     * @param array $items Raw menu items from database
     * @param bool $preserveHashUrls Whether to preserve # URLs for mmenu compatibility
     * @return array Processed menu items
     */
    function _processMenuItems($items, $preserveHashUrls = false) {
        $processed = array();

        foreach ($items as $item) {
            $url = $item['NavigationMenu']['url'];

            // Handle URL processing based on preserveHashUrls flag
            if ($preserveHashUrls) {
                // For mmenu, keep # URLs as-is, only convert empty URLs to false
                $processedUrl = empty($url) ? false : $url;
            } else {
                // For other navigation types, convert both # and empty URLs to false
                $processedUrl = ($url === '#' || empty($url)) ? false : $url;
            }

            $menuItem = array(
                'text' => $item['NavigationMenu']['name'],
                'url' => $processedUrl
            );

            // Add children if they exist
            if (!empty($item['children'])) {
                $menuItem['has_children'] = true;
                $menuItem['children'] = $this->_processMenuItems($item['children'], $preserveHashUrls);
            }

            $processed[] = $menuItem;
        }

        return $processed;
    }

    /**
     * Get menu types for form dropdown
     *
     * @return array Menu types
     */
    function getMenuTypes() {
        return array(
            'far_wide' => 'Far & Wide',
            'main_nav' => 'Main Navigation',
            'footer' => 'Footer'
        );
    }

    /**
     * After save callback to clear navigation cache
     */
    function afterSave($created) {
        parent::afterSave($created);
        $this->clearNavigationCache();
    }

    /**
     * After delete callback to clear navigation cache
     */
    function afterDelete() {
        parent::afterDelete();
        $this->clearNavigationCache();
    }

    /**
     * Clear navigation cache
     */
    function clearNavigationCache() {
        Cache::delete('navigation_menu_far_wide', 'navigation');
        Cache::delete('main_navigation', 'navigation');
    }


}

?>
